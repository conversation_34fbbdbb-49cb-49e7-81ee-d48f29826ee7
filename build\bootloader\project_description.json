{"version": "1", "project_name": "bootloader", "project_version": "v5.1.2", "project_path": "D:/esp/v5.1.2/esp-idf/components/bootloader/subproject", "idf_path": "D:/esp/v5.1.2/esp-idf", "build_dir": "D:/vscode/projects-lvgl/uart_echo/build/bootloader", "config_file": "D:/vscode/projects-lvgl/uart_echo/sdkconfig", "config_defaults": "", "bootloader_elf": "", "app_elf": "bootloader.elf", "app_bin": "bootloader.bin", "build_type": "flash_app", "git_revision": "v5.1.2", "target": "esp32", "rev": "0", "min_rev": "0", "max_rev": "399", "phy_data_partition": "", "monitor_baud": "115200", "monitor_toolprefix": "xtensa-esp32-elf-", "c_compiler": "D:/esp/Espressif/tools/xtensa-esp32-elf/esp-12.2.0_20230208/xtensa-esp32-elf/bin/xtensa-esp32-elf-gcc.exe", "config_environment": {"COMPONENT_KCONFIGS": "D:/esp/v5.1.2/esp-idf/components/efuse/Kconfig;D:/esp/v5.1.2/esp-idf/components/esp_common/Kconfig;D:/esp/v5.1.2/esp-idf/components/esp_hw_support/Kconfig;D:/esp/v5.1.2/esp-idf/components/esp_system/Kconfig;D:/esp/v5.1.2/esp-idf/components/freertos/Kconfig;D:/esp/v5.1.2/esp-idf/components/hal/Kconfig;D:/esp/v5.1.2/esp-idf/components/log/Kconfig;D:/esp/v5.1.2/esp-idf/components/newlib/Kconfig;D:/esp/v5.1.2/esp-idf/components/soc/Kconfig;D:/esp/v5.1.2/esp-idf/components/spi_flash/Kconfig", "COMPONENT_KCONFIGS_PROJBUILD": "D:/esp/v5.1.2/esp-idf/components/bootloader/Kconfig.projbuild;D:/esp/v5.1.2/esp-idf/components/esp_app_format/Kconfig.projbuild;D:/esp/v5.1.2/esp-idf/components/esp_rom/Kconfig.projbuild;D:/esp/v5.1.2/esp-idf/components/esptool_py/Kconfig.projbuild;D:/esp/v5.1.2/esp-idf/components/partition_table/Kconfig.projbuild"}, "common_component_reqs": ["log", "esp_rom", "esp_common", "esp_hw_support", "newlib", "xtensa"], "build_components": ["bootloader", "bootloader_support", "efuse", "esp_app_format", "esp_common", "esp_hw_support", "esp_rom", "esp_system", "esptool_py", "freertos", "hal", "log", "main", "micro-ecc", "newlib", "partition_table", "soc", "spi_flash", "xtensa", ""], "build_component_paths": ["D:/esp/v5.1.2/esp-idf/components/bootloader", "D:/esp/v5.1.2/esp-idf/components/bootloader_support", "D:/esp/v5.1.2/esp-idf/components/efuse", "D:/esp/v5.1.2/esp-idf/components/esp_app_format", "D:/esp/v5.1.2/esp-idf/components/esp_common", "D:/esp/v5.1.2/esp-idf/components/esp_hw_support", "D:/esp/v5.1.2/esp-idf/components/esp_rom", "D:/esp/v5.1.2/esp-idf/components/esp_system", "D:/esp/v5.1.2/esp-idf/components/esptool_py", "D:/esp/v5.1.2/esp-idf/components/freertos", "D:/esp/v5.1.2/esp-idf/components/hal", "D:/esp/v5.1.2/esp-idf/components/log", "D:/esp/v5.1.2/esp-idf/components/bootloader/subproject/main", "D:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc", "D:/esp/v5.1.2/esp-idf/components/newlib", "D:/esp/v5.1.2/esp-idf/components/partition_table", "D:/esp/v5.1.2/esp-idf/components/soc", "D:/esp/v5.1.2/esp-idf/components/spi_flash", "D:/esp/v5.1.2/esp-idf/components/xtensa", ""], "build_component_info": {"bootloader": {"alias": "idf::bootloader", "target": "___idf_bootloader", "prefix": "idf", "dir": "D:/esp/v5.1.2/esp-idf/components/bootloader", "type": "CONFIG_ONLY", "lib": "__idf_bootloader", "reqs": [], "priv_reqs": ["partition_table", "esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "bootloader_support": {"alias": "idf::bootloader_support", "target": "___idf_bootloader_support", "prefix": "idf", "dir": "D:/esp/v5.1.2/esp-idf/components/bootloader_support", "type": "LIBRARY", "lib": "__idf_bootloader_support", "reqs": ["soc"], "priv_reqs": ["micro-ecc", "spi_flash", "efuse", "esp_app_format", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/vscode/projects-lvgl/uart_echo/build/bootloader/esp-idf/bootloader_support/libbootloader_support.a", "sources": ["D:/esp/v5.1.2/esp-idf/components/bootloader_support/src/bootloader_common.c", "D:/esp/v5.1.2/esp-idf/components/bootloader_support/src/bootloader_common_loader.c", "D:/esp/v5.1.2/esp-idf/components/bootloader_support/src/bootloader_clock_init.c", "D:/esp/v5.1.2/esp-idf/components/bootloader_support/src/bootloader_mem.c", "D:/esp/v5.1.2/esp-idf/components/bootloader_support/src/bootloader_random.c", "D:/esp/v5.1.2/esp-idf/components/bootloader_support/src/bootloader_random_esp32.c", "D:/esp/v5.1.2/esp-idf/components/bootloader_support/src/bootloader_efuse.c", "D:/esp/v5.1.2/esp-idf/components/bootloader_support/src/flash_encrypt.c", "D:/esp/v5.1.2/esp-idf/components/bootloader_support/src/secure_boot.c", "D:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/src/bootloader_flash.c", "D:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/src/flash_qio_mode.c", "D:/esp/v5.1.2/esp-idf/components/bootloader_support/bootloader_flash/src/bootloader_flash_config_esp32.c", "D:/esp/v5.1.2/esp-idf/components/bootloader_support/src/bootloader_utility.c", "D:/esp/v5.1.2/esp-idf/components/bootloader_support/src/flash_partitions.c", "D:/esp/v5.1.2/esp-idf/components/bootloader_support/src/esp_image_format.c", "D:/esp/v5.1.2/esp-idf/components/bootloader_support/src/bootloader_init.c", "D:/esp/v5.1.2/esp-idf/components/bootloader_support/src/bootloader_clock_loader.c", "D:/esp/v5.1.2/esp-idf/components/bootloader_support/src/bootloader_console.c", "D:/esp/v5.1.2/esp-idf/components/bootloader_support/src/bootloader_console_loader.c", "D:/esp/v5.1.2/esp-idf/components/bootloader_support/src/esp32/bootloader_sha.c", "D:/esp/v5.1.2/esp-idf/components/bootloader_support/src/esp32/bootloader_soc.c", "D:/esp/v5.1.2/esp-idf/components/bootloader_support/src/esp32/bootloader_esp32.c", "D:/esp/v5.1.2/esp-idf/components/bootloader_support/src/bootloader_panic.c"], "include_dirs": ["include", "bootloader_flash/include", "private_include"]}, "efuse": {"alias": "idf::efuse", "target": "___idf_efuse", "prefix": "idf", "dir": "D:/esp/v5.1.2/esp-idf/components/efuse", "type": "LIBRARY", "lib": "__idf_efuse", "reqs": [], "priv_reqs": ["bootloader_support", "soc", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/vscode/projects-lvgl/uart_echo/build/bootloader/esp-idf/efuse/libefuse.a", "sources": ["D:/esp/v5.1.2/esp-idf/components/efuse/esp32/esp_efuse_table.c", "D:/esp/v5.1.2/esp-idf/components/efuse/esp32/esp_efuse_fields.c", "D:/esp/v5.1.2/esp-idf/components/efuse/esp32/esp_efuse_utility.c", "D:/esp/v5.1.2/esp-idf/components/efuse/src/esp_efuse_api.c", "D:/esp/v5.1.2/esp-idf/components/efuse/src/esp_efuse_fields.c", "D:/esp/v5.1.2/esp-idf/components/efuse/src/esp_efuse_utility.c", "D:/esp/v5.1.2/esp-idf/components/efuse/src/efuse_controller/keys/without_key_purposes/three_key_blocks/esp_efuse_api_key.c"], "include_dirs": ["include", "esp32/include"]}, "esp_app_format": {"alias": "idf::esp_app_format", "target": "___idf_esp_app_format", "prefix": "idf", "dir": "D:/esp/v5.1.2/esp-idf/components/esp_app_format", "type": "LIBRARY", "lib": "__idf_esp_app_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/vscode/projects-lvgl/uart_echo/build/bootloader/esp-idf/esp_app_format/libesp_app_format.a", "sources": ["D:/esp/v5.1.2/esp-idf/components/esp_app_format/esp_app_desc.c"], "include_dirs": ["include"]}, "esp_common": {"alias": "idf::esp_common", "target": "___idf_esp_common", "prefix": "idf", "dir": "D:/esp/v5.1.2/esp-idf/components/esp_common", "type": "LIBRARY", "lib": "__idf_esp_common", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/vscode/projects-lvgl/uart_echo/build/bootloader/esp-idf/esp_common/libesp_common.a", "sources": ["D:/esp/v5.1.2/esp-idf/components/esp_common/src/esp_err_to_name.c"], "include_dirs": ["include"]}, "esp_hw_support": {"alias": "idf::esp_hw_support", "target": "___idf_esp_hw_support", "prefix": "idf", "dir": "D:/esp/v5.1.2/esp-idf/components/esp_hw_support", "type": "LIBRARY", "lib": "__idf_esp_hw_support", "reqs": ["soc"], "priv_reqs": ["efuse", "spi_flash", "bootloader_support", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/vscode/projects-lvgl/uart_echo/build/bootloader/esp-idf/esp_hw_support/libesp_hw_support.a", "sources": ["D:/esp/v5.1.2/esp-idf/components/esp_hw_support/cpu.c", "D:/esp/v5.1.2/esp-idf/components/esp_hw_support/esp_memory_utils.c", "D:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32/cpu_region_protect.c", "D:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32/rtc_clk.c", "D:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32/rtc_clk_init.c", "D:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32/rtc_init.c", "D:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32/rtc_sleep.c", "D:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32/rtc_time.c", "D:/esp/v5.1.2/esp-idf/components/esp_hw_support/port/esp32/chip_info.c"], "include_dirs": ["include", "include/soc", "include/soc/esp32"]}, "esp_rom": {"alias": "idf::esp_rom", "target": "___idf_esp_rom", "prefix": "idf", "dir": "D:/esp/v5.1.2/esp-idf/components/esp_rom", "type": "LIBRARY", "lib": "__idf_esp_rom", "reqs": [], "priv_reqs": ["soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/vscode/projects-lvgl/uart_echo/build/bootloader/esp-idf/esp_rom/libesp_rom.a", "sources": ["D:/esp/v5.1.2/esp-idf/components/esp_rom/patches/esp_rom_crc.c", "D:/esp/v5.1.2/esp-idf/components/esp_rom/patches/esp_rom_sys.c", "D:/esp/v5.1.2/esp-idf/components/esp_rom/patches/esp_rom_uart.c", "D:/esp/v5.1.2/esp-idf/components/esp_rom/patches/esp_rom_spiflash.c", "D:/esp/v5.1.2/esp-idf/components/esp_rom/patches/esp_rom_efuse.c", "D:/esp/v5.1.2/esp-idf/components/esp_rom/patches/esp_rom_longjmp.S"], "include_dirs": ["include", "include/esp32", "esp32"]}, "esp_system": {"alias": "idf::esp_system", "target": "___idf_esp_system", "prefix": "idf", "dir": "D:/esp/v5.1.2/esp-idf/components/esp_system", "type": "LIBRARY", "lib": "__idf_esp_system", "reqs": ["spi_flash"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/vscode/projects-lvgl/uart_echo/build/bootloader/esp-idf/esp_system/libesp_system.a", "sources": ["D:/esp/v5.1.2/esp-idf/components/esp_system/esp_err.c"], "include_dirs": []}, "esptool_py": {"alias": "idf::esptool_py", "target": "___idf_esptool_py", "prefix": "idf", "dir": "D:/esp/v5.1.2/esp-idf/components/esptool_py", "type": "CONFIG_ONLY", "lib": "__idf_esptool_py", "reqs": ["bootloader"], "priv_reqs": ["partition_table"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "freertos": {"alias": "idf::freertos", "target": "___idf_freertos", "prefix": "idf", "dir": "D:/esp/v5.1.2/esp-idf/components/freertos", "type": "CONFIG_ONLY", "lib": "__idf_freertos", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "hal": {"alias": "idf::hal", "target": "___idf_hal", "prefix": "idf", "dir": "D:/esp/v5.1.2/esp-idf/components/hal", "type": "LIBRARY", "lib": "__idf_hal", "reqs": ["soc", "esp_rom"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/vscode/projects-lvgl/uart_echo/build/bootloader/esp-idf/hal/libhal.a", "sources": ["D:/esp/v5.1.2/esp-idf/components/hal/mpu_hal.c", "D:/esp/v5.1.2/esp-idf/components/hal/efuse_hal.c", "D:/esp/v5.1.2/esp-idf/components/hal/esp32/efuse_hal.c", "D:/esp/v5.1.2/esp-idf/components/hal/wdt_hal_iram.c", "D:/esp/v5.1.2/esp-idf/components/hal/mmu_hal.c", "D:/esp/v5.1.2/esp-idf/components/hal/esp32/cache_hal_esp32.c"], "include_dirs": ["esp32/include", "include", "platform_port/include"]}, "log": {"alias": "idf::log", "target": "___idf_log", "prefix": "idf", "dir": "D:/esp/v5.1.2/esp-idf/components/log", "type": "LIBRARY", "lib": "__idf_log", "reqs": [], "priv_reqs": ["soc", "hal", "esp_hw_support"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/vscode/projects-lvgl/uart_echo/build/bootloader/esp-idf/log/liblog.a", "sources": ["D:/esp/v5.1.2/esp-idf/components/log/log.c", "D:/esp/v5.1.2/esp-idf/components/log/log_buffers.c", "D:/esp/v5.1.2/esp-idf/components/log/log_noos.c"], "include_dirs": ["include"]}, "main": {"alias": "idf::main", "target": "___idf_main", "prefix": "idf", "dir": "D:/esp/v5.1.2/esp-idf/components/bootloader/subproject/main", "type": "LIBRARY", "lib": "__idf_main", "reqs": ["bootloader", "bootloader_support"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/vscode/projects-lvgl/uart_echo/build/bootloader/esp-idf/main/libmain.a", "sources": ["D:/esp/v5.1.2/esp-idf/components/bootloader/subproject/main/bootloader_start.c"], "include_dirs": []}, "micro-ecc": {"alias": "idf::micro-ecc", "target": "___idf_micro-ecc", "prefix": "idf", "dir": "D:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc", "type": "LIBRARY", "lib": "__idf_micro-ecc", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/vscode/projects-lvgl/uart_echo/build/bootloader/esp-idf/micro-ecc/libmicro-ecc.a", "sources": ["D:/esp/v5.1.2/esp-idf/components/bootloader/subproject/components/micro-ecc/uECC_verify_antifault.c"], "include_dirs": [".", "micro-ecc"]}, "newlib": {"alias": "idf::newlib", "target": "___idf_newlib", "prefix": "idf", "dir": "D:/esp/v5.1.2/esp-idf/components/newlib", "type": "CONFIG_ONLY", "lib": "__idf_newlib", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["platform_include"]}, "partition_table": {"alias": "idf::partition_table", "target": "___idf_partition_table", "prefix": "idf", "dir": "D:/esp/v5.1.2/esp-idf/components/partition_table", "type": "CONFIG_ONLY", "lib": "__idf_partition_table", "reqs": [], "priv_reqs": ["esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "soc": {"alias": "idf::soc", "target": "___idf_soc", "prefix": "idf", "dir": "D:/esp/v5.1.2/esp-idf/components/soc", "type": "LIBRARY", "lib": "__idf_soc", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/vscode/projects-lvgl/uart_echo/build/bootloader/esp-idf/soc/libsoc.a", "sources": ["D:/esp/v5.1.2/esp-idf/components/soc/lldesc.c", "D:/esp/v5.1.2/esp-idf/components/soc/dport_access_common.c", "D:/esp/v5.1.2/esp-idf/components/soc/esp32/interrupts.c", "D:/esp/v5.1.2/esp-idf/components/soc/esp32/gpio_periph.c", "D:/esp/v5.1.2/esp-idf/components/soc/esp32/uart_periph.c", "D:/esp/v5.1.2/esp-idf/components/soc/esp32/dport_access.c", "D:/esp/v5.1.2/esp-idf/components/soc/esp32/adc_periph.c", "D:/esp/v5.1.2/esp-idf/components/soc/esp32/spi_periph.c", "D:/esp/v5.1.2/esp-idf/components/soc/esp32/ledc_periph.c", "D:/esp/v5.1.2/esp-idf/components/soc/esp32/pcnt_periph.c", "D:/esp/v5.1.2/esp-idf/components/soc/esp32/rmt_periph.c", "D:/esp/v5.1.2/esp-idf/components/soc/esp32/sdm_periph.c", "D:/esp/v5.1.2/esp-idf/components/soc/esp32/i2s_periph.c", "D:/esp/v5.1.2/esp-idf/components/soc/esp32/i2c_periph.c", "D:/esp/v5.1.2/esp-idf/components/soc/esp32/timer_periph.c", "D:/esp/v5.1.2/esp-idf/components/soc/esp32/lcd_periph.c", "D:/esp/v5.1.2/esp-idf/components/soc/esp32/mcpwm_periph.c", "D:/esp/v5.1.2/esp-idf/components/soc/esp32/sdmmc_periph.c", "D:/esp/v5.1.2/esp-idf/components/soc/esp32/touch_sensor_periph.c", "D:/esp/v5.1.2/esp-idf/components/soc/esp32/twai_periph.c", "D:/esp/v5.1.2/esp-idf/components/soc/esp32/dac_periph.c", "D:/esp/v5.1.2/esp-idf/components/soc/esp32/rtc_io_periph.c", "D:/esp/v5.1.2/esp-idf/components/soc/esp32/sdio_slave_periph.c"], "include_dirs": ["include", "esp32", "esp32/include"]}, "spi_flash": {"alias": "idf::spi_flash", "target": "___idf_spi_flash", "prefix": "idf", "dir": "D:/esp/v5.1.2/esp-idf/components/spi_flash", "type": "LIBRARY", "lib": "__idf_spi_flash", "reqs": ["hal"], "priv_reqs": ["bootloader_support", "soc"], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/vscode/projects-lvgl/uart_echo/build/bootloader/esp-idf/spi_flash/libspi_flash.a", "sources": ["D:/esp/v5.1.2/esp-idf/components/spi_flash/spi_flash_wrap.c"], "include_dirs": ["include"]}, "xtensa": {"alias": "idf::xtensa", "target": "___idf_xtensa", "prefix": "idf", "dir": "D:/esp/v5.1.2/esp-idf/components/xtensa", "type": "LIBRARY", "lib": "__idf_xtensa", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "D:/vscode/projects-lvgl/uart_echo/build/bootloader/esp-idf/xtensa/libxtensa.a", "sources": ["D:/esp/v5.1.2/esp-idf/components/xtensa/eri.c", "D:/esp/v5.1.2/esp-idf/components/xtensa/xt_trax.c"], "include_dirs": ["include", "esp32/include"]}}, "debug_prefix_map_gdbinit": ""}