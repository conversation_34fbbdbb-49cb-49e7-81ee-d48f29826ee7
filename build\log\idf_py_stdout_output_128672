-- ccache will be used for faster recompilation
-- git rev-parse returned 'fatal: not a git repository (or any of the parent directories): .git'
-- Could not use 'git describe' to determine PROJECT_VER.
-- Building ESP-IDF components for target esp32
-- Project sdkconfig file D:/vscode/projects-lvgl/uart_echo/sdkconfig
-- App "uart_echo" version: 1
-- Adding linker script D:/vscode/projects-lvgl/uart_echo/build/esp-idf/esp_system/ld/memory.ld
-- Adding linker script D:/esp/v5.1.2/esp-idf/components/esp_system/ld/esp32/sections.ld.in
-- Adding linker script D:/esp/v5.1.2/esp-idf/components/esp_rom/esp32/ld/esp32.rom.ld
-- Adding linker script D:/esp/v5.1.2/esp-idf/components/esp_rom/esp32/ld/esp32.rom.api.ld
-- Adding linker script D:/esp/v5.1.2/esp-idf/components/esp_rom/esp32/ld/esp32.rom.libgcc.ld
-- Adding linker script D:/esp/v5.1.2/esp-idf/components/esp_rom/esp32/ld/esp32.rom.newlib-data.ld
-- Adding linker script D:/esp/v5.1.2/esp-idf/components/esp_rom/esp32/ld/esp32.rom.syscalls.ld
-- Adding linker script D:/esp/v5.1.2/esp-idf/components/esp_rom/esp32/ld/esp32.rom.newlib-funcs.ld
-- Adding linker script D:/esp/v5.1.2/esp-idf/components/soc/esp32/ld/esp32.peripherals.ld
-- Components: app_trace app_update bootloader bootloader_support bt cmock console cxx driver efuse esp-tls esp_adc esp_app_format esp_coex esp_common esp_eth esp_event esp_gdbstub esp_hid esp_http_client esp_http_server esp_https_ota esp_https_server esp_hw_support esp_lcd esp_local_ctrl esp_mm esp_netif esp_netif_stack esp_partition esp_phy esp_pm esp_psram esp_ringbuf esp_rom esp_system esp_timer esp_wifi espcoredump esptool_py fatfs freertos hal heap http_parser idf_test ieee802154 json log lwip main mbedtls mqtt newlib nvs_flash openthread partition_table perfmon protobuf-c protocomm pthread sdmmc soc spi_flash spiffs tcp_transport ulp unity usb vfs wear_levelling wifi_provisioning wpa_supplicant xtensa
-- Component paths: D:/esp/v5.1.2/esp-idf/components/app_trace D:/esp/v5.1.2/esp-idf/components/app_update D:/esp/v5.1.2/esp-idf/components/bootloader D:/esp/v5.1.2/esp-idf/components/bootloader_support D:/esp/v5.1.2/esp-idf/components/bt D:/esp/v5.1.2/esp-idf/components/cmock D:/esp/v5.1.2/esp-idf/components/console D:/esp/v5.1.2/esp-idf/components/cxx D:/esp/v5.1.2/esp-idf/components/driver D:/esp/v5.1.2/esp-idf/components/efuse D:/esp/v5.1.2/esp-idf/components/esp-tls D:/esp/v5.1.2/esp-idf/components/esp_adc D:/esp/v5.1.2/esp-idf/components/esp_app_format D:/esp/v5.1.2/esp-idf/components/esp_coex D:/esp/v5.1.2/esp-idf/components/esp_common D:/esp/v5.1.2/esp-idf/components/esp_eth D:/esp/v5.1.2/esp-idf/components/esp_event D:/esp/v5.1.2/esp-idf/components/esp_gdbstub D:/esp/v5.1.2/esp-idf/components/esp_hid D:/esp/v5.1.2/esp-idf/components/esp_http_client D:/esp/v5.1.2/esp-idf/components/esp_http_server D:/esp/v5.1.2/esp-idf/components/esp_https_ota D:/esp/v5.1.2/esp-idf/components/esp_https_server D:/esp/v5.1.2/esp-idf/components/esp_hw_support D:/esp/v5.1.2/esp-idf/components/esp_lcd D:/esp/v5.1.2/esp-idf/components/esp_local_ctrl D:/esp/v5.1.2/esp-idf/components/esp_mm D:/esp/v5.1.2/esp-idf/components/esp_netif D:/esp/v5.1.2/esp-idf/components/esp_netif_stack D:/esp/v5.1.2/esp-idf/components/esp_partition D:/esp/v5.1.2/esp-idf/components/esp_phy D:/esp/v5.1.2/esp-idf/components/esp_pm D:/esp/v5.1.2/esp-idf/components/esp_psram D:/esp/v5.1.2/esp-idf/components/esp_ringbuf D:/esp/v5.1.2/esp-idf/components/esp_rom D:/esp/v5.1.2/esp-idf/components/esp_system D:/esp/v5.1.2/esp-idf/components/esp_timer D:/esp/v5.1.2/esp-idf/components/esp_wifi D:/esp/v5.1.2/esp-idf/components/espcoredump D:/esp/v5.1.2/esp-idf/components/esptool_py D:/esp/v5.1.2/esp-idf/components/fatfs D:/esp/v5.1.2/esp-idf/components/freertos D:/esp/v5.1.2/esp-idf/components/hal D:/esp/v5.1.2/esp-idf/components/heap D:/esp/v5.1.2/esp-idf/components/http_parser D:/esp/v5.1.2/esp-idf/components/idf_test D:/esp/v5.1.2/esp-idf/components/ieee802154 D:/esp/v5.1.2/esp-idf/components/json D:/esp/v5.1.2/esp-idf/components/log D:/esp/v5.1.2/esp-idf/components/lwip D:/vscode/projects-lvgl/uart_echo/main D:/esp/v5.1.2/esp-idf/components/mbedtls D:/esp/v5.1.2/esp-idf/components/mqtt D:/esp/v5.1.2/esp-idf/components/newlib D:/esp/v5.1.2/esp-idf/components/nvs_flash D:/esp/v5.1.2/esp-idf/components/openthread D:/esp/v5.1.2/esp-idf/components/partition_table D:/esp/v5.1.2/esp-idf/components/perfmon D:/esp/v5.1.2/esp-idf/components/protobuf-c D:/esp/v5.1.2/esp-idf/components/protocomm D:/esp/v5.1.2/esp-idf/components/pthread D:/esp/v5.1.2/esp-idf/components/sdmmc D:/esp/v5.1.2/esp-idf/components/soc D:/esp/v5.1.2/esp-idf/components/spi_flash D:/esp/v5.1.2/esp-idf/components/spiffs D:/esp/v5.1.2/esp-idf/components/tcp_transport D:/esp/v5.1.2/esp-idf/components/ulp D:/esp/v5.1.2/esp-idf/components/unity D:/esp/v5.1.2/esp-idf/components/usb D:/esp/v5.1.2/esp-idf/components/vfs D:/esp/v5.1.2/esp-idf/components/wear_levelling D:/esp/v5.1.2/esp-idf/components/wifi_provisioning D:/esp/v5.1.2/esp-idf/components/wpa_supplicant D:/esp/v5.1.2/esp-idf/components/xtensa
-- Configuring done
-- Generating done
-- Build files have been written to: D:/vscode/projects-lvgl/uart_echo/build
