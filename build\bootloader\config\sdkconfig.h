/*
 * Automatically generated file. DO NOT EDIT.
 * Espressif IoT Development Framework (ESP-IDF) 5.1.2 Configuration Header
 */
#pragma once
#define CONFIG_SOC_BROWNOUT_RESET_SUPPORTED "Not determined"
#define CONFIG_SOC_TWAI_BRP_DIV_SUPPORTED "Not determined"
#define CONFIG_SOC_DPORT_WORKAROUND "Not determined"
#define CONFIG_SOC_CAPS_ECO_VER_MAX 301
#define CONFIG_SOC_ADC_SUPPORTED 1
#define CONFIG_SOC_DAC_SUPPORTED 1
#define CONFIG_SOC_UART_SUPPORTED 1
#define CONFIG_SOC_MCPWM_SUPPORTED 1
#define CONFIG_SOC_GPTIMER_SUPPORTED 1
#define CONFIG_SOC_SDMMC_HOST_SUPPORTED 1
#define CONFIG_SOC_BT_SUPPORTED 1
#define CONFIG_SOC_PCNT_SUPPORTED 1
#define CONFIG_SOC_WIFI_SUPPORTED 1
#define CONFIG_SOC_SDIO_SLAVE_SUPPORTED 1
#define CONFIG_SOC_TWAI_SUPPORTED 1
#define CONFIG_SOC_EMAC_SUPPORTED 1
#define CONFIG_SOC_ULP_SUPPORTED 1
#define CONFIG_SOC_CCOMP_TIMER_SUPPORTED 1
#define CONFIG_SOC_RTC_FAST_MEM_SUPPORTED 1
#define CONFIG_SOC_RTC_SLOW_MEM_SUPPORTED 1
#define CONFIG_SOC_RTC_MEM_SUPPORTED 1
#define CONFIG_SOC_I2S_SUPPORTED 1
#define CONFIG_SOC_RMT_SUPPORTED 1
#define CONFIG_SOC_SDM_SUPPORTED 1
#define CONFIG_SOC_GPSPI_SUPPORTED 1
#define CONFIG_SOC_LEDC_SUPPORTED 1
#define CONFIG_SOC_I2C_SUPPORTED 1
#define CONFIG_SOC_SUPPORT_COEXISTENCE 1
#define CONFIG_SOC_AES_SUPPORTED 1
#define CONFIG_SOC_MPI_SUPPORTED 1
#define CONFIG_SOC_SHA_SUPPORTED 1
#define CONFIG_SOC_FLASH_ENC_SUPPORTED 1
#define CONFIG_SOC_SECURE_BOOT_SUPPORTED 1
#define CONFIG_SOC_TOUCH_SENSOR_SUPPORTED 1
#define CONFIG_SOC_BOD_SUPPORTED 1
#define CONFIG_SOC_ULP_FSM_SUPPORTED 1
#define CONFIG_SOC_DPORT_WORKAROUND_DIS_INTERRUPT_LVL 5
#define CONFIG_SOC_XTAL_SUPPORT_26M 1
#define CONFIG_SOC_XTAL_SUPPORT_40M 1
#define CONFIG_SOC_XTAL_SUPPORT_AUTO_DETECT 1
#define CONFIG_SOC_ADC_RTC_CTRL_SUPPORTED 1
#define CONFIG_SOC_ADC_DIG_CTRL_SUPPORTED 1
#define CONFIG_SOC_ADC_DMA_SUPPORTED 1
#define CONFIG_SOC_ADC_PERIPH_NUM 2
#define CONFIG_SOC_ADC_MAX_CHANNEL_NUM 10
#define CONFIG_SOC_ADC_ATTEN_NUM 4
#define CONFIG_SOC_ADC_DIGI_CONTROLLER_NUM 2
#define CONFIG_SOC_ADC_PATT_LEN_MAX 16
#define CONFIG_SOC_ADC_DIGI_MIN_BITWIDTH 9
#define CONFIG_SOC_ADC_DIGI_MAX_BITWIDTH 12
#define CONFIG_SOC_ADC_DIGI_RESULT_BYTES 2
#define CONFIG_SOC_ADC_DIGI_DATA_BYTES_PER_CONV 4
#define CONFIG_SOC_ADC_SAMPLE_FREQ_THRES_HIGH 2
#define CONFIG_SOC_ADC_SAMPLE_FREQ_THRES_LOW 20
#define CONFIG_SOC_ADC_RTC_MIN_BITWIDTH 9
#define CONFIG_SOC_ADC_RTC_MAX_BITWIDTH 12
#define CONFIG_SOC_SHARED_IDCACHE_SUPPORTED 1
#define CONFIG_SOC_IDCACHE_PER_CORE 1
#define CONFIG_SOC_CPU_CORES_NUM 2
#define CONFIG_SOC_CPU_INTR_NUM 32
#define CONFIG_SOC_CPU_HAS_FPU 1
#define CONFIG_SOC_CPU_BREAKPOINTS_NUM 2
#define CONFIG_SOC_CPU_WATCHPOINTS_NUM 2
#define CONFIG_SOC_CPU_WATCHPOINT_SIZE 64
#define CONFIG_SOC_DAC_CHAN_NUM 2
#define CONFIG_SOC_DAC_RESOLUTION 8
#define CONFIG_SOC_DAC_DMA_16BIT_ALIGN 1
#define CONFIG_SOC_GPIO_PORT 1
#define CONFIG_SOC_GPIO_PIN_COUNT 40
#define CONFIG_SOC_GPIO_VALID_GPIO_MASK 0xFFFFFFFFFF
#define CONFIG_SOC_GPIO_VALID_DIGITAL_IO_PAD_MASK 0xEF0FEA
#define CONFIG_SOC_I2C_NUM 2
#define CONFIG_SOC_I2C_FIFO_LEN 32
#define CONFIG_SOC_I2C_CMD_REG_NUM 16
#define CONFIG_SOC_I2C_SUPPORT_SLAVE 1
#define CONFIG_SOC_I2C_SUPPORT_APB 1
#define CONFIG_SOC_I2S_NUM 2
#define CONFIG_SOC_I2S_HW_VERSION_1 1
#define CONFIG_SOC_I2S_SUPPORTS_APLL 1
#define CONFIG_SOC_I2S_SUPPORTS_PLL_F160M 1
#define CONFIG_SOC_I2S_SUPPORTS_PDM 1
#define CONFIG_SOC_I2S_SUPPORTS_PDM_TX 1
#define CONFIG_SOC_I2S_PDM_MAX_TX_LINES 1
#define CONFIG_SOC_I2S_SUPPORTS_PDM_RX 1
#define CONFIG_SOC_I2S_PDM_MAX_RX_LINES 1
#define CONFIG_SOC_I2S_SUPPORTS_ADC_DAC 1
#define CONFIG_SOC_I2S_SUPPORTS_ADC 1
#define CONFIG_SOC_I2S_SUPPORTS_DAC 1
#define CONFIG_SOC_I2S_SUPPORTS_LCD_CAMERA 1
#define CONFIG_SOC_I2S_TRANS_SIZE_ALIGN_WORD 1
#define CONFIG_SOC_I2S_LCD_I80_VARIANT 1
#define CONFIG_SOC_LCD_I80_SUPPORTED 1
#define CONFIG_SOC_LCD_I80_BUSES 2
#define CONFIG_SOC_LCD_I80_BUS_WIDTH 24
#define CONFIG_SOC_LEDC_HAS_TIMER_SPECIFIC_MUX 1
#define CONFIG_SOC_LEDC_SUPPORT_APB_CLOCK 1
#define CONFIG_SOC_LEDC_SUPPORT_REF_TICK 1
#define CONFIG_SOC_LEDC_SUPPORT_HS_MODE 1
#define CONFIG_SOC_LEDC_CHANNEL_NUM 8
#define CONFIG_SOC_LEDC_TIMER_BIT_WIDTH 20
#define CONFIG_SOC_MCPWM_GROUPS 2
#define CONFIG_SOC_MCPWM_TIMERS_PER_GROUP 3
#define CONFIG_SOC_MCPWM_OPERATORS_PER_GROUP 3
#define CONFIG_SOC_MCPWM_COMPARATORS_PER_OPERATOR 2
#define CONFIG_SOC_MCPWM_GENERATORS_PER_OPERATOR 2
#define CONFIG_SOC_MCPWM_TRIGGERS_PER_OPERATOR 2
#define CONFIG_SOC_MCPWM_GPIO_FAULTS_PER_GROUP 3
#define CONFIG_SOC_MCPWM_CAPTURE_TIMERS_PER_GROUP 1
#define CONFIG_SOC_MCPWM_CAPTURE_CHANNELS_PER_TIMER 3
#define CONFIG_SOC_MCPWM_GPIO_SYNCHROS_PER_GROUP 3
#define CONFIG_SOC_MMU_PERIPH_NUM 2
#define CONFIG_SOC_MMU_LINEAR_ADDRESS_REGION_NUM 3
#define CONFIG_SOC_MPU_MIN_REGION_SIZE 0x20000000
#define CONFIG_SOC_MPU_REGIONS_MAX_NUM 8
#define CONFIG_SOC_PCNT_GROUPS 1
#define CONFIG_SOC_PCNT_UNITS_PER_GROUP 8
#define CONFIG_SOC_PCNT_CHANNELS_PER_UNIT 2
#define CONFIG_SOC_PCNT_THRES_POINT_PER_UNIT 2
#define CONFIG_SOC_RMT_GROUPS 1
#define CONFIG_SOC_RMT_TX_CANDIDATES_PER_GROUP 8
#define CONFIG_SOC_RMT_RX_CANDIDATES_PER_GROUP 8
#define CONFIG_SOC_RMT_CHANNELS_PER_GROUP 8
#define CONFIG_SOC_RMT_MEM_WORDS_PER_CHANNEL 64
#define CONFIG_SOC_RMT_SUPPORT_REF_TICK 1
#define CONFIG_SOC_RMT_SUPPORT_APB 1
#define CONFIG_SOC_RMT_CHANNEL_CLK_INDEPENDENT 1
#define CONFIG_SOC_RTCIO_PIN_COUNT 18
#define CONFIG_SOC_RTCIO_INPUT_OUTPUT_SUPPORTED 1
#define CONFIG_SOC_RTCIO_HOLD_SUPPORTED 1
#define CONFIG_SOC_RTCIO_WAKE_SUPPORTED 1
#define CONFIG_SOC_SDM_GROUPS 1
#define CONFIG_SOC_SDM_CHANNELS_PER_GROUP 8
#define CONFIG_SOC_SDM_CLK_SUPPORT_APB 1
#define CONFIG_SOC_SPI_HD_BOTH_INOUT_SUPPORTED 1
#define CONFIG_SOC_SPI_AS_CS_SUPPORTED 1
#define CONFIG_SOC_SPI_PERIPH_NUM 3
#define CONFIG_SOC_SPI_DMA_CHAN_NUM 2
#define CONFIG_SOC_SPI_MAX_CS_NUM 3
#define CONFIG_SOC_SPI_SUPPORT_CLK_APB 1
#define CONFIG_SOC_SPI_MAXIMUM_BUFFER_SIZE 64
#define CONFIG_SOC_SPI_MAX_PRE_DIVIDER 8192
#define CONFIG_SOC_MEMSPI_SRC_FREQ_80M_SUPPORTED 1
#define CONFIG_SOC_MEMSPI_SRC_FREQ_40M_SUPPORTED 1
#define CONFIG_SOC_MEMSPI_SRC_FREQ_26M_SUPPORTED 1
#define CONFIG_SOC_MEMSPI_SRC_FREQ_20M_SUPPORTED 1
#define CONFIG_SOC_TIMER_GROUPS 2
#define CONFIG_SOC_TIMER_GROUP_TIMERS_PER_GROUP 2
#define CONFIG_SOC_TIMER_GROUP_COUNTER_BIT_WIDTH 64
#define CONFIG_SOC_TIMER_GROUP_TOTAL_TIMERS 4
#define CONFIG_SOC_TIMER_GROUP_SUPPORT_APB 1
#define CONFIG_SOC_TOUCH_VERSION_1 1
#define CONFIG_SOC_TOUCH_SENSOR_NUM 10
#define CONFIG_SOC_TOUCH_PAD_MEASURE_WAIT_MAX 0xFF
#define CONFIG_SOC_TWAI_CONTROLLER_NUM 1
#define CONFIG_SOC_TWAI_BRP_MIN 2
#define CONFIG_SOC_TWAI_CLK_SUPPORT_APB 1
#define CONFIG_SOC_TWAI_SUPPORT_MULTI_ADDRESS_LAYOUT 1
#define CONFIG_SOC_UART_NUM 3
#define CONFIG_SOC_UART_SUPPORT_APB_CLK 1
#define CONFIG_SOC_UART_SUPPORT_REF_TICK 1
#define CONFIG_SOC_UART_FIFO_LEN 128
#define CONFIG_SOC_UART_BITRATE_MAX 5000000
#define CONFIG_SOC_SPIRAM_SUPPORTED 1
#define CONFIG_SOC_SPI_MEM_SUPPORT_CONFIG_GPIO_BY_EFUSE 1
#define CONFIG_SOC_SHA_SUPPORT_PARALLEL_ENG 1
#define CONFIG_SOC_SHA_SUPPORT_SHA1 1
#define CONFIG_SOC_SHA_SUPPORT_SHA256 1
#define CONFIG_SOC_SHA_SUPPORT_SHA384 1
#define CONFIG_SOC_SHA_SUPPORT_SHA512 1
#define CONFIG_SOC_RSA_MAX_BIT_LEN 4096
#define CONFIG_SOC_AES_SUPPORT_AES_128 1
#define CONFIG_SOC_AES_SUPPORT_AES_192 1
#define CONFIG_SOC_AES_SUPPORT_AES_256 1
#define CONFIG_SOC_SECURE_BOOT_V1 1
#define CONFIG_SOC_EFUSE_SECURE_BOOT_KEY_DIGESTS 1
#define CONFIG_SOC_FLASH_ENCRYPTED_XTS_AES_BLOCK_MAX 32
#define CONFIG_SOC_PHY_DIG_REGS_MEM_SIZE 21
#define CONFIG_SOC_PM_SUPPORT_EXT0_WAKEUP 1
#define CONFIG_SOC_PM_SUPPORT_EXT1_WAKEUP 1
#define CONFIG_SOC_PM_SUPPORT_EXT_WAKEUP 1
#define CONFIG_SOC_PM_SUPPORT_TOUCH_SENSOR_WAKEUP 1
#define CONFIG_SOC_PM_SUPPORT_RTC_PERIPH_PD 1
#define CONFIG_SOC_PM_SUPPORT_RTC_FAST_MEM_PD 1
#define CONFIG_SOC_PM_SUPPORT_RTC_SLOW_MEM_PD 1
#define CONFIG_SOC_PM_SUPPORT_RC_FAST_PD 1
#define CONFIG_SOC_PM_SUPPORT_VDDSDIO_PD 1
#define CONFIG_SOC_PM_SUPPORT_MODEM_PD 1
#define CONFIG_SOC_CONFIGURABLE_VDDSDIO_SUPPORTED 1
#define CONFIG_SOC_CLK_APLL_SUPPORTED 1
#define CONFIG_SOC_APLL_MULTIPLIER_OUT_MIN_HZ 350000000
#define CONFIG_SOC_APLL_MULTIPLIER_OUT_MAX_HZ 500000000
#define CONFIG_SOC_APLL_MIN_HZ 5303031
#define CONFIG_SOC_APLL_MAX_HZ 125000000
#define CONFIG_SOC_CLK_RC_FAST_D256_SUPPORTED 1
#define CONFIG_SOC_RTC_SLOW_CLK_SUPPORT_RC_FAST_D256 1
#define CONFIG_SOC_CLK_RC_FAST_SUPPORT_CALIBRATION 1
#define CONFIG_SOC_CLK_XTAL32K_SUPPORTED 1
#define CONFIG_SOC_SDMMC_USE_IOMUX 1
#define CONFIG_SOC_SDMMC_NUM_SLOTS 2
#define CONFIG_SOC_WIFI_WAPI_SUPPORT 1
#define CONFIG_SOC_WIFI_CSI_SUPPORT 1
#define CONFIG_SOC_WIFI_MESH_SUPPORT 1
#define CONFIG_SOC_WIFI_SUPPORT_VARIABLE_BEACON_WINDOW 1
#define CONFIG_SOC_WIFI_NAN_SUPPORT 1
#define CONFIG_SOC_BLE_SUPPORTED 1
#define CONFIG_SOC_BLE_MESH_SUPPORTED 1
#define CONFIG_SOC_BT_CLASSIC_SUPPORTED 1
#define CONFIG_SOC_BLUFI_SUPPORTED 1
#define CONFIG_SOC_ULP_HAS_ADC 1
#define CONFIG_SOC_PHY_COMBO_MODULE 1
#define CONFIG_IDF_CMAKE 1
#define CONFIG_IDF_TARGET_ARCH_XTENSA 1
#define CONFIG_IDF_TARGET_ARCH "xtensa"
#define CONFIG_IDF_TARGET "esp32"
#define CONFIG_IDF_TARGET_ESP32 1
#define CONFIG_IDF_FIRMWARE_CHIP_ID 0x0000
#define CONFIG_APP_BUILD_TYPE_APP_2NDBOOT 1
#define CONFIG_APP_BUILD_GENERATE_BINARIES 1
#define CONFIG_APP_BUILD_BOOTLOADER 1
#define CONFIG_APP_BUILD_USE_FLASH_SECTIONS 1
#define CONFIG_BOOTLOADER_OFFSET_IN_FLASH 0x1000
#define CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_SIZE 1
#define CONFIG_BOOTLOADER_LOG_LEVEL_INFO 1
#define CONFIG_BOOTLOADER_LOG_LEVEL 3
#define CONFIG_BOOTLOADER_VDDSDIO_BOOST_1_9V 1
#define CONFIG_BOOTLOADER_REGION_PROTECTION_ENABLE 1
#define CONFIG_BOOTLOADER_WDT_ENABLE 1
#define CONFIG_BOOTLOADER_WDT_TIME_MS 9000
#define CONFIG_BOOTLOADER_RESERVE_RTC_SIZE 0x0
#define CONFIG_BOOTLOADER_FLASH_XMC_SUPPORT 1
#define CONFIG_SECURE_BOOT_V1_SUPPORTED 1
#define CONFIG_APP_COMPILE_TIME_DATE 1
#define CONFIG_APP_RETRIEVE_LEN_ELF_SHA 16
#define CONFIG_ESP_ROM_HAS_CRC_LE 1
#define CONFIG_ESP_ROM_HAS_CRC_BE 1
#define CONFIG_ESP_ROM_HAS_MZ_CRC32 1
#define CONFIG_ESP_ROM_HAS_JPEG_DECODE 1
#define CONFIG_ESP_ROM_HAS_UART_BUF_SWITCH 1
#define CONFIG_ESP_ROM_NEEDS_SWSETUP_WORKAROUND 1
#define CONFIG_ESP_ROM_HAS_NEWLIB_NANO_FORMAT 1
#define CONFIG_ESPTOOLPY_FLASHMODE_DIO 1
#define CONFIG_ESPTOOLPY_FLASH_SAMPLE_MODE_STR 1
#define CONFIG_ESPTOOLPY_FLASHMODE "dio"
#define CONFIG_ESPTOOLPY_FLASHFREQ_40M 1
#define CONFIG_ESPTOOLPY_FLASHFREQ "40m"
#define CONFIG_ESPTOOLPY_FLASHSIZE_2MB 1
#define CONFIG_ESPTOOLPY_FLASHSIZE "2MB"
#define CONFIG_ESPTOOLPY_BEFORE_RESET 1
#define CONFIG_ESPTOOLPY_BEFORE "default_reset"
#define CONFIG_ESPTOOLPY_AFTER_RESET 1
#define CONFIG_ESPTOOLPY_AFTER "hard_reset"
#define CONFIG_ESPTOOLPY_MONITOR_BAUD 115200
#define CONFIG_PARTITION_TABLE_SINGLE_APP 1
#define CONFIG_PARTITION_TABLE_CUSTOM_FILENAME "partitions.csv"
#define CONFIG_PARTITION_TABLE_FILENAME "partitions_singleapp.csv"
#define CONFIG_PARTITION_TABLE_OFFSET 0x8000
#define CONFIG_PARTITION_TABLE_MD5 1
#define CONFIG_COMPILER_OPTIMIZATION_DEFAULT 1
#define CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE 1
#define CONFIG_COMPILER_FLOAT_LIB_FROM_GCCLIB 1
#define CONFIG_COMPILER_OPTIMIZATION_ASSERTION_LEVEL 2
#define CONFIG_COMPILER_HIDE_PATHS_MACROS 1
#define CONFIG_COMPILER_STACK_CHECK_MODE_NONE 1
#define CONFIG_EFUSE_CODE_SCHEME_COMPAT_3_4 1
#define CONFIG_EFUSE_MAX_BLK_LEN 192
#define CONFIG_ESP_ERR_TO_NAME_LOOKUP 1
#define CONFIG_ESP32_REV_MIN_0 1
#define CONFIG_ESP32_REV_MIN 0
#define CONFIG_ESP32_REV_MIN_FULL 0
#define CONFIG_ESP_REV_MIN_FULL 0
#define CONFIG_ESP32_REV_MAX_FULL 399
#define CONFIG_ESP_REV_MAX_FULL 399
#define CONFIG_ESP_MAC_ADDR_UNIVERSE_WIFI_STA 1
#define CONFIG_ESP_MAC_ADDR_UNIVERSE_WIFI_AP 1
#define CONFIG_ESP_MAC_ADDR_UNIVERSE_BT 1
#define CONFIG_ESP_MAC_ADDR_UNIVERSE_ETH 1
#define CONFIG_ESP_MAC_UNIVERSAL_MAC_ADDRESSES_FOUR 1
#define CONFIG_ESP32_UNIVERSAL_MAC_ADDRESSES_FOUR 1
#define CONFIG_ESP32_UNIVERSAL_MAC_ADDRESSES 4
#define CONFIG_ESP_SLEEP_FLASH_LEAKAGE_WORKAROUND 1
#define CONFIG_ESP_SLEEP_RTC_BUS_ISO_WORKAROUND 1
#define CONFIG_ESP_SLEEP_DEEP_SLEEP_WAKEUP_DELAY 2000
#define CONFIG_ESP_SLEEP_GPIO_ENABLE_INTERNAL_RESISTORS 1
#define CONFIG_RTC_CLK_SRC_INT_RC 1
#define CONFIG_RTC_CLK_CAL_CYCLES 1024
#define CONFIG_PERIPH_CTRL_FUNC_IN_IRAM 1
#define CONFIG_XTAL_FREQ_40 1
#define CONFIG_XTAL_FREQ 40
#define CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_160 1
#define CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ 160
#define CONFIG_ESP32_TRACEMEM_RESERVE_DRAM 0x0
#define CONFIG_ESP_SYSTEM_PANIC_PRINT_REBOOT 1
#define CONFIG_ESP_SYSTEM_PANIC_REBOOT_DELAY_SECONDS 0
#define CONFIG_ESP_SYSTEM_EVENT_QUEUE_SIZE 32
#define CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE 2304
#define CONFIG_ESP_MAIN_TASK_STACK_SIZE 3584
#define CONFIG_ESP_MAIN_TASK_AFFINITY_CPU0 1
#define CONFIG_ESP_MAIN_TASK_AFFINITY 0x0
#define CONFIG_ESP_MINIMAL_SHARED_STACK_SIZE 2048
#define CONFIG_ESP_CONSOLE_UART_DEFAULT 1
#define CONFIG_ESP_CONSOLE_UART 1
#define CONFIG_ESP_CONSOLE_MULTIPLE_UART 1
#define CONFIG_ESP_CONSOLE_UART_NUM 0
#define CONFIG_ESP_CONSOLE_UART_BAUDRATE 115200
#define CONFIG_ESP_INT_WDT 1
#define CONFIG_ESP_INT_WDT_TIMEOUT_MS 300
#define CONFIG_ESP_INT_WDT_CHECK_CPU1 1
#define CONFIG_ESP_TASK_WDT_EN 1
#define CONFIG_ESP_TASK_WDT_INIT 1
#define CONFIG_ESP_TASK_WDT_TIMEOUT_S 5
#define CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0 1
#define CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU1 1
#define CONFIG_ESP_DEBUG_OCDAWARE 1
#define CONFIG_ESP_SYSTEM_CHECK_INT_LEVEL_4 1
#define CONFIG_ESP_BROWNOUT_DET 1
#define CONFIG_ESP_BROWNOUT_DET_LVL_SEL_0 1
#define CONFIG_ESP_BROWNOUT_DET_LVL 0
#define CONFIG_ESP_SYSTEM_BROWNOUT_INTR 1
#define CONFIG_ESP_IPC_TASK_STACK_SIZE 1024
#define CONFIG_ESP_IPC_USES_CALLERS_PRIORITY 1
#define CONFIG_ESP_IPC_ISR_ENABLE 1
#define CONFIG_FREERTOS_HZ 100
#define CONFIG_FREERTOS_CHECK_STACKOVERFLOW_CANARY 1
#define CONFIG_FREERTOS_THREAD_LOCAL_STORAGE_POINTERS 1
#define CONFIG_FREERTOS_IDLE_TASK_STACKSIZE 1536
#define CONFIG_FREERTOS_MAX_TASK_NAME_LEN 16
#define CONFIG_FREERTOS_TIMER_TASK_PRIORITY 1
#define CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH 2048
#define CONFIG_FREERTOS_TIMER_QUEUE_LENGTH 10
#define CONFIG_FREERTOS_QUEUE_REGISTRY_SIZE 0
#define CONFIG_FREERTOS_TASK_NOTIFICATION_ARRAY_ENTRIES 1
#define CONFIG_FREERTOS_TASK_FUNCTION_WRAPPER 1
#define CONFIG_FREERTOS_TLSP_DELETION_CALLBACKS 1
#define CONFIG_FREERTOS_CHECK_MUTEX_GIVEN_BY_OWNER 1
#define CONFIG_FREERTOS_ISR_STACKSIZE 1536
#define CONFIG_FREERTOS_INTERRUPT_BACKTRACE 1
#define CONFIG_FREERTOS_TICK_SUPPORT_CORETIMER 1
#define CONFIG_FREERTOS_CORETIMER_0 1
#define CONFIG_FREERTOS_SYSTICK_USES_CCOUNT 1
#define CONFIG_FREERTOS_ENABLE_TASK_SNAPSHOT 1
#define CONFIG_FREERTOS_NO_AFFINITY 0x7FFFFFFF
#define CONFIG_FREERTOS_SUPPORT_STATIC_ALLOCATION 1
#define CONFIG_FREERTOS_DEBUG_OCDAWARE 1
#define CONFIG_HAL_ASSERTION_EQUALS_SYSTEM 1
#define CONFIG_HAL_DEFAULT_ASSERTION_LEVEL 2
#define CONFIG_LOG_DEFAULT_LEVEL_INFO 1
#define CONFIG_LOG_DEFAULT_LEVEL 3
#define CONFIG_LOG_MAXIMUM_EQUALS_DEFAULT 1
#define CONFIG_LOG_MAXIMUM_LEVEL 3
#define CONFIG_LOG_COLORS 1
#define CONFIG_LOG_TIMESTAMP_SOURCE_RTOS 1
#define CONFIG_NEWLIB_STDOUT_LINE_ENDING_CRLF 1
#define CONFIG_NEWLIB_STDIN_LINE_ENDING_CR 1
#define CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC_HRT 1
#define CONFIG_MMU_PAGE_SIZE_64KB 1
#define CONFIG_MMU_PAGE_MODE "64KB"
#define CONFIG_MMU_PAGE_SIZE 0x10000
#define CONFIG_SPI_FLASH_ROM_DRIVER_PATCH 1
#define CONFIG_SPI_FLASH_DANGEROUS_WRITE_ABORTS 1
#define CONFIG_SPI_FLASH_YIELD_DURING_ERASE 1
#define CONFIG_SPI_FLASH_ERASE_YIELD_DURATION_MS 20
#define CONFIG_SPI_FLASH_ERASE_YIELD_TICKS 1
#define CONFIG_SPI_FLASH_WRITE_CHUNK_SIZE 8192
#define CONFIG_SPI_FLASH_BROWNOUT_RESET_XMC 1
#define CONFIG_SPI_FLASH_BROWNOUT_RESET 1
#define CONFIG_SPI_FLASH_VENDOR_XMC_SUPPORTED 1
#define CONFIG_SPI_FLASH_VENDOR_GD_SUPPORTED 1
#define CONFIG_SPI_FLASH_VENDOR_ISSI_SUPPORTED 1
#define CONFIG_SPI_FLASH_VENDOR_MXIC_SUPPORTED 1
#define CONFIG_SPI_FLASH_VENDOR_WINBOND_SUPPORTED 1
#define CONFIG_SPI_FLASH_SUPPORT_ISSI_CHIP 1
#define CONFIG_SPI_FLASH_SUPPORT_MXIC_CHIP 1
#define CONFIG_SPI_FLASH_SUPPORT_GD_CHIP 1
#define CONFIG_SPI_FLASH_SUPPORT_WINBOND_CHIP 1
#define CONFIG_SPI_FLASH_ENABLE_ENCRYPTED_READ_WRITE 1

/* List of deprecated options */
#define CONFIG_BROWNOUT_DET CONFIG_ESP_BROWNOUT_DET
#define CONFIG_BROWNOUT_DET_LVL CONFIG_ESP_BROWNOUT_DET_LVL
#define CONFIG_BROWNOUT_DET_LVL_SEL_0 CONFIG_ESP_BROWNOUT_DET_LVL_SEL_0
#define CONFIG_COMPILER_OPTIMIZATION_LEVEL_DEBUG CONFIG_COMPILER_OPTIMIZATION_DEFAULT
#define CONFIG_CONSOLE_UART CONFIG_ESP_CONSOLE_UART
#define CONFIG_CONSOLE_UART_BAUDRATE CONFIG_ESP_CONSOLE_UART_BAUDRATE
#define CONFIG_CONSOLE_UART_DEFAULT CONFIG_ESP_CONSOLE_UART_DEFAULT
#define CONFIG_CONSOLE_UART_NUM CONFIG_ESP_CONSOLE_UART_NUM
#define CONFIG_ESP32_BROWNOUT_DET CONFIG_ESP_BROWNOUT_DET
#define CONFIG_ESP32_BROWNOUT_DET_LVL CONFIG_ESP_BROWNOUT_DET_LVL
#define CONFIG_ESP32_BROWNOUT_DET_LVL_SEL_0 CONFIG_ESP_BROWNOUT_DET_LVL_SEL_0
#define CONFIG_ESP32_DEBUG_OCDAWARE CONFIG_ESP_DEBUG_OCDAWARE
#define CONFIG_ESP32_DEEP_SLEEP_WAKEUP_DELAY CONFIG_ESP_SLEEP_DEEP_SLEEP_WAKEUP_DELAY
#define CONFIG_ESP32_DEFAULT_CPU_FREQ_160 CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_160
#define CONFIG_ESP32_DEFAULT_CPU_FREQ_MHZ CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ
#define CONFIG_ESP32_PANIC_PRINT_REBOOT CONFIG_ESP_SYSTEM_PANIC_PRINT_REBOOT
#define CONFIG_ESP32_RTC_CLK_CAL_CYCLES CONFIG_RTC_CLK_CAL_CYCLES
#define CONFIG_ESP32_RTC_CLK_SRC_INT_RC CONFIG_RTC_CLK_SRC_INT_RC
#define CONFIG_ESP32_RTC_CLOCK_SOURCE_INTERNAL_RC CONFIG_RTC_CLK_SRC_INT_RC
#define CONFIG_ESP32_TIME_SYSCALL_USE_RTC_FRC1 CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC_HRT
#define CONFIG_ESP32_TIME_SYSCALL_USE_RTC_HRT CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC_HRT
#define CONFIG_ESP32_XTAL_FREQ CONFIG_XTAL_FREQ
#define CONFIG_ESP32_XTAL_FREQ_40 CONFIG_XTAL_FREQ_40
#define CONFIG_ESP_TASK_WDT CONFIG_ESP_TASK_WDT_INIT
#define CONFIG_FLASHMODE_DIO CONFIG_ESPTOOLPY_FLASHMODE_DIO
#define CONFIG_FOUR_UNIVERSAL_MAC_ADDRESS CONFIG_ESP32_UNIVERSAL_MAC_ADDRESSES_FOUR
#define CONFIG_INT_WDT CONFIG_ESP_INT_WDT
#define CONFIG_INT_WDT_CHECK_CPU1 CONFIG_ESP_INT_WDT_CHECK_CPU1
#define CONFIG_INT_WDT_TIMEOUT_MS CONFIG_ESP_INT_WDT_TIMEOUT_MS
#define CONFIG_IPC_TASK_STACK_SIZE CONFIG_ESP_IPC_TASK_STACK_SIZE
#define CONFIG_LOG_BOOTLOADER_LEVEL CONFIG_BOOTLOADER_LOG_LEVEL
#define CONFIG_LOG_BOOTLOADER_LEVEL_INFO CONFIG_BOOTLOADER_LOG_LEVEL_INFO
#define CONFIG_MAIN_TASK_STACK_SIZE CONFIG_ESP_MAIN_TASK_STACK_SIZE
#define CONFIG_MONITOR_BAUD CONFIG_ESPTOOLPY_MONITOR_BAUD
#define CONFIG_NUMBER_OF_UNIVERSAL_MAC_ADDRESS CONFIG_ESP32_UNIVERSAL_MAC_ADDRESSES
#define CONFIG_OPTIMIZATION_ASSERTIONS_ENABLED CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE
#define CONFIG_OPTIMIZATION_ASSERTION_LEVEL CONFIG_COMPILER_OPTIMIZATION_ASSERTION_LEVEL
#define CONFIG_OPTIMIZATION_LEVEL_DEBUG CONFIG_COMPILER_OPTIMIZATION_DEFAULT
#define CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_ABORTS CONFIG_SPI_FLASH_DANGEROUS_WRITE_ABORTS
#define CONFIG_STACK_CHECK_NONE CONFIG_COMPILER_STACK_CHECK_MODE_NONE
#define CONFIG_SYSTEM_EVENT_QUEUE_SIZE CONFIG_ESP_SYSTEM_EVENT_QUEUE_SIZE
#define CONFIG_SYSTEM_EVENT_TASK_STACK_SIZE CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE
#define CONFIG_TASK_WDT CONFIG_ESP_TASK_WDT_INIT
#define CONFIG_TASK_WDT_CHECK_IDLE_TASK_CPU0 CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0
#define CONFIG_TASK_WDT_CHECK_IDLE_TASK_CPU1 CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU1
#define CONFIG_TASK_WDT_TIMEOUT_S CONFIG_ESP_TASK_WDT_TIMEOUT_S
#define CONFIG_TIMER_QUEUE_LENGTH CONFIG_FREERTOS_TIMER_QUEUE_LENGTH
#define CONFIG_TIMER_TASK_PRIORITY CONFIG_FREERTOS_TIMER_TASK_PRIORITY
#define CONFIG_TIMER_TASK_STACK_DEPTH CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH
#define CONFIG_TRACEMEM_RESERVE_DRAM CONFIG_ESP32_TRACEMEM_RESERVE_DRAM
