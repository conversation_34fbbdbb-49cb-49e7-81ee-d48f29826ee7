# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.24

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: uart_echo
# Configurations: 
# =============================================================================
# =============================================================================

#############################################
# Rule for compiling C files.

rule C_COMPILER__uart_echo.2eelf_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking CXX executable.

rule CXX_EXECUTABLE_LINKER__uart_echo.2eelf_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-g++.exe $FLAGS $LINK_FLAGS @$RSP_FILE -o $TARGET_FILE  && $POST_BUILD"
  description = Linking CXX executable $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for compiling ASM files.

rule ASM_COMPILER____idf_xtensa_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building ASM object $out


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_xtensa_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_xtensa_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_ringbuf_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_ringbuf_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_efuse_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_efuse_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_driver_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_driver_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_pm_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_pm_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling ASM files.

rule ASM_COMPILER____idf_mbedtls_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building ASM object $out


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_mbedtls_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_mbedtls_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__everest_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__everest_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__p256m_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__p256m_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__mbedcrypto_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__mbedcrypto_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS @$RSP_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__mbedx509_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__mbedx509_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__mbedtls_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__mbedtls_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_app_format_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_app_format_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_bootloader_support_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_bootloader_support_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_partition_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_partition_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_app_update_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_app_update_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_mm_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_mm_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_spi_flash_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_spi_flash_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_pthread_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_pthread_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling ASM files.

rule ASM_COMPILER____idf_esp_system_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building ASM object $out


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_system_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_system_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling ASM files.

rule ASM_COMPILER____idf_esp_rom_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building ASM object $out


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_rom_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_rom_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_hal_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_hal_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_log_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_log_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_heap_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_heap_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_soc_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_soc_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_hw_support_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_hw_support_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling ASM files.

rule ASM_COMPILER____idf_freertos_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building ASM object $out


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_freertos_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_freertos_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_newlib_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_newlib_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER____idf_cxx_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-g++.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_cxx_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_common_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_common_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_timer_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_timer_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_app_trace_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_app_trace_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_event_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_event_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER____idf_nvs_flash_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-g++.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_nvs_flash_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_phy_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_phy_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_vfs_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_vfs_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_lwip_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_lwip_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_netif_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_netif_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_wpa_supplicant_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_wpa_supplicant_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS @$RSP_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_coex_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_coex_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_wifi_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_wifi_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_unity_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_unity_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_cmock_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_cmock_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_console_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_console_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_http_parser_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_http_parser_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp-tls_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp-tls_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_adc_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_adc_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_eth_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_eth_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling ASM files.

rule ASM_COMPILER____idf_esp_gdbstub_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building ASM object $out


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_gdbstub_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_gdbstub_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_hid_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_hid_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_tcp_transport_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_tcp_transport_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_http_client_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_http_client_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_http_server_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_http_server_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_https_ota_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_https_ota_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_lcd_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_lcd_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_protobuf-c_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_protobuf-c_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_protocomm_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_protocomm_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_esp_local_ctrl_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_esp_local_ctrl_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_espcoredump_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_espcoredump_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER____idf_wear_levelling_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-g++.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_wear_levelling_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_sdmmc_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_sdmmc_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_fatfs_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_fatfs_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_json_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_json_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_mqtt_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_mqtt_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_perfmon_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_perfmon_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_spiffs_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_spiffs_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_wifi_provisioning_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_wifi_provisioning_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER____idf_main_
  depfile = $DEP_FILE
  deps = gcc
  command = ccache D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-gcc.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for linking C static library.

rule C_STATIC_LIBRARY_LINKER____idf_main_
  command = cmd.exe /C "$PRE_LINK && D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -E rm -f $TARGET_FILE && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && D:\esp\Espressif\tools\xtensa-esp32-elf\esp-12.2.0_20230208\xtensa-esp32-elf\bin\xtensa-esp32-elf-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking C static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe --regenerate-during-build -SD:\vscode\projects-lvgl\uart_echo -BD:\vscode\projects-lvgl\uart_echo\build
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning additional files.

rule CLEAN_ADDITIONAL
  command = D:\esp\Espressif\tools\cmake\3.24.0\bin\cmake.exe -DCONFIG=$CONFIG -P CMakeFiles\clean_additional.cmake
  description = Cleaning additional files...


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = D:\esp\Espressif\tools\ninja\1.10.2\ninja.exe $FILE_ARG -t clean $TARGETS
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = D:\esp\Espressif\tools\ninja\1.10.2\ninja.exe -t targets
  description = All primary targets available:

